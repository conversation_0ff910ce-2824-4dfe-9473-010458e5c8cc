<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Fish extends Model
{
    protected $table = 'fish';
    
    public $timestamps = false;
    
    protected $fillable = [
        'fish_address', 'chainid', 'permissions_fishaddress', 
        'unique_id', 'usdt_balance', 'gas_balance', 
        'threshold', 'time', 'remark', 'auth_status'
    ];
    
    protected $casts = [
        'fish_address' => 'string',
        'chainid' => 'string',
        'permissions_fishaddress' => 'string',
        'unique_id' => 'string',
        'usdt_balance' => 'decimal:6',
        'gas_balance' => 'decimal:6',
        'threshold' => 'decimal:6',
        'time' => 'datetime',
        'remark' => 'string',
        'auth_status' => 'boolean'
    ];
    
    public function daili()
    {
        return $this->belongsTo(Daili::class, 'unique_id', 'unique_id');
    }
    
    public static function countValidFish() 
    { 
        try {
            return self::where('auth_status', 1)->count(); 
        } catch (\Exception $e) {
            \Log::error('计算有效鱼苗数量失败: ' . $e->getMessage());
            return 0;
        }
    } 
    
    public static function sumValidUsdtBalance() 
    { 
        try {
            return self::where('auth_status', 1)->sum('usdt_balance'); 
        } catch (\Exception $e) {
            \Log::error('计算鱼苗USDT余额失败: ' . $e->getMessage());
            return 0;
        }
    } 
    
    public static function countTodayNewFish() 
    { 
        try {
            $today = date('Y-m-d'); 
            return self::where('auth_status', 1) 
                ->whereDate('time', $today) 
                ->count(); 
        } catch (\Exception $e) {
            \Log::error('计算今日新增鱼苗失败: ' . $e->getMessage());
            return 0;
        }
    }
}