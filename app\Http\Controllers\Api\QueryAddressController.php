<?php
namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class QueryAddressController extends Controller
{
    public function query(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fish_address' => 'required|string',
            'chainid' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'message' => $validator->errors()->first()
            ]);
        }
        
        $fishAddress = $request->input('fish_address');
        $chainId = $request->input('chainid');
        
        try {
            $exists = DB::table('fish')
                ->where('fish_address', $fishAddress)
                ->where('chainid', $chainId)
                ->where('auth_status', 1)
                ->exists();
                
            return response()->json([
                'status' => 'success',
                'result' => $exists ? 'yes' : 'no'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => '查询执行失败: ' . $e->getMessage()
            ]);
        }
    }
}