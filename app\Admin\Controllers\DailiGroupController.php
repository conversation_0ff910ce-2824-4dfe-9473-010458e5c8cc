<?php
namespace App\Admin\Controllers;
use App\Models\Daili;
use App\Models\DailiGroup;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;

class DailiGroupController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!auth('admin')->check() || !auth('admin')->user()->can('manage_daili_groups')) {
                abort(403, '未授权访问');
            }
            return $next($request);
        });
    }
    
    public function index(Content $content)
    {
        try {
            $totalGroups = DailiGroup::count();
            
            $statCard = new Card('总代群统计', "
                <div class='row text-center'>
                    <div class='col-md-12'>
                        <h3>总代群数量</h3>
                        <h2 class='text-primary'>{$totalGroups}</h2>
                    </div>
                </div>
            ");
            
            return $content
                ->header('总代群管理')
                ->description('总代群管理')
                ->body($statCard)
                ->body($this->grid());
        } catch (\Exception $e) {
            \Log::error('DailiGroup管理异常: ' . $e->getMessage());
            return $content
                ->header('总代群管理')
                ->description('总代群管理')
                ->body(new Card('错误', '加载数据时发生错误，请稍后再试'));
        }
    }
    
    protected function grid()
    {
        $grid = new Grid(new DailiGroup());
        
        $grid->model()->orderBy('id', 'desc');
        
        $grid->column('id', 'ID')->sortable();
        $grid->column('groupid', '总代群ID')->sortable();
        $grid->column('remark', '备注')->sortable();
        $grid->column('share_profits', '分润比例')->sortable()->display(function ($value) {
            return htmlspecialchars(floatval($value) * 100) . '%';
        });
        $grid->column('status', '上下课状态')->sortable()->switch();
        
        $grid->column('daili_count', '渔夫数量')->display(function () {
            return Daili::where('groupid', $this->groupid)->count();
        });
        
        $grid->quickSearch('groupid', 'remark');
        
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            $actions->disableView();
        });
        
        return $grid;
    }
    
    public function create(Content $content)
    {
        return $content
            ->header('新增总代群')
            ->description('创建一个新总代群')
            ->body($this->form());
    }
    
    public function edit($id, Content $content)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $content
            ->header('编辑总代群')
            ->description('修改总代群信息')
            ->body($this->form()->edit($id));
    }
    
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'groupid' => 'required|string|max:191',
            'remark' => 'nullable|string|max:191',
            'share_profits' => 'required|numeric|min:0|max:1',
            'status' => 'required|in:0,1',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        $request->merge([
            'share_profits' => number_format(floatval($request->input('share_profits')), 2, '.', '')
        ]);
        
        return $this->form()->store();
    }
    
    public function update($id, Request $request)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        $validator = Validator::make($request->all(), [
            'groupid' => 'required|string|max:191',
            'remark' => 'nullable|string|max:191',
            'share_profits' => 'required|numeric|min:0|max:1',
            'status' => 'required|in:0,1',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        $request->merge([
            'share_profits' => number_format(floatval($request->input('share_profits')), 2, '.', '')
        ]);
        
        return $this->form()->update($id);
    }
    
    public function destroy($id)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $this->form()->destroy($id);
    }
    
    protected function form()
    {
        $form = new Form(new DailiGroup());
        
        $form->text('groupid', '总代群ID')
            ->required()
            ->maxLength(191)
            ->help('这里填写总代群的CHATID，将 @BKCBOT 机器人拉入你的群聊发送关键词 id 即可查询 CHATID');
            
        $form->text('remark', '备注')
            ->maxLength(191)
            ->help('填写群组备注名称');
            
        $form->select('share_profits', '分润比例')
            ->options($this->getShareProfitsOptions())
            ->default('0.50')
            ->help('分润比例说明：设置80%代表划扣的80%余额都转给渔夫，设置100%代表划扣的所有余额都转给渔夫，设置0%代表所有余额都转给自己');
            
        $form->switch('status', '状态')
            ->default(1)
            ->help('上课:可正常使用，下课:停止使用');
        
        $form->saving(function (Form $form) {
            if (isset($form->share_profits)) {
                $form->share_profits = number_format(floatval($form->share_profits), 2, '.', '');
            }
        });
        
        $form->tools(function (Form\Tools $tools) {
            $tools->disableView();
        });
        
        return $form;
    }

    protected function getShareProfitsOptions()
    {
        $options = [];
        
        for ($i = 0; $i <= 100; $i++) {
            $value = number_format($i / 100, 2, '.', '');
            $options[$value] = $i . '%';
        }
        
        return $options;
    }
}