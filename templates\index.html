{% extends "demo_base.html" %}

{% block title %}TRC20授权Demo - 首页{% endblock %}

{% block content %}
<div class="header">
    <h1>TRC20授权Demo</h1>
    <p>模拟原系统的授权流程</p>
</div>

<div class="content">
    <div class="order-info">
        <h3>订单信息</h3>
        <div class="order-item">
            <span>订单号:</span>
            <span>{{ order.order_sn }}</span>
        </div>
        <div class="order-item">
            <span>商品名称:</span>
            <span>{{ order.title }}</span>
        </div>
        <div class="order-item">
            <span>购买数量:</span>
            <span>{{ order.buy_amount }}</span>
        </div>
        <div class="order-item">
            <span>商品单价:</span>
            <span>${{ order.goods_price }}</span>
        </div>
        <div class="order-item">
            <span>实付金额:</span>
            <span>${{ order.actual_price }}</span>
        </div>
    </div>
    
    <button class="pay-button" onclick="goToPay()">
        前往支付
    </button>
    
    <div id="statusMessage" class="status-message"></div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function goToPay() {
        const orderSn = '{{ order.order_sn }}';
        window.location.href = `/bill/${orderSn}`;
    }
</script>
{% endblock %}
