<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Daili extends Model
{
    protected $table = 'daili';
    
    public $timestamps = false;
    
    protected $fillable = [
        'unique_id', 'tguid', 'username', 'fullName', 
        'fishnumber', 'time', 'remark', 'payment_address', 
        'groupid', 'threshold'
    ];
    
    protected $casts = [
        'unique_id' => 'string',
        'tguid' => 'string',
        'username' => 'string',
        'fullName' => 'string',
        'fishnumber' => 'integer',
        'time' => 'datetime',
        'payment_address' => 'string',
        'groupid' => 'string',
        'threshold' => 'integer'
    ];
    
    public function group()
    {
        return $this->belongsTo(DailiGroup::class, 'groupid', 'groupid');
    }
    
    public static function getTotalCount()
    {
        return self::count();
    }
    
    public static function getTodayCount()
    {
        $today = date('Y-m-d');
        return self::whereDate('time', $today)->count();
    }
    
    public static function getGroupName($groupId)
    {
        if (empty($groupId)) {
            return '未知群组';
        }
        
        $group = DailiGroup::where('groupid', $groupId)->first();
        return $group ? $group->remark : $groupId;
    }
    
    public static function getGroupOptions()
    {
        try {
            return DailiGroup::pluck('remark', 'groupid')->toArray();
        } catch (\Exception $e) {
            \Log::error('获取群组选项失败: ' . $e->getMessage());
            return [];
        }
    }
    
    public static function generateUniqueId()
    {
        $attempts = 0;
        $maxAttempts = 10;
        
        do {
            if ($attempts >= $maxAttempts) {
                \Log::error('生成唯一ID失败: 超过最大尝试次数');
                return date('YmdHis') . mt_rand(100, 999);
            }
            
            $uniqueId = (string)mt_rand(100000000, 999999999);
            $attempts++;
        } while (self::where('unique_id', $uniqueId)->exists());
        
        return $uniqueId;
    }
    
    public static function validateTronAddress($address)
    {
        if (empty($address)) {
            return true;
        }
        
        return preg_match('/^T[a-zA-Z0-9]{33}$/', $address);
    }
}