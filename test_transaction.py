#!/usr/bin/env python3
"""
测试TRC20授权交易构造
验证生成的交易数据格式是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from trc20_approval_demo import construct_trc20_approve_transaction, CONFIG

def test_approve_transaction():
    """测试授权交易构造"""
    print("=" * 60)
    print("TRC20授权交易构造测试")
    print("=" * 60)
    
    # 测试参数
    user_address = "TLsV52sRDL79HXGGm9yzwKibb6BeruhUzy"  # 示例用户地址
    spender_address = CONFIG['permission_address']  # 系统权限地址
    amount = CONFIG['authorized_amount']  # 授权金额
    
    print(f"测试参数:")
    print(f"  用户地址: {user_address}")
    print(f"  被授权地址: {spender_address}")
    print(f"  授权金额: {amount} (表示 {int(amount)/1000000} USDT)")
    print()
    
    # 构造交易
    transaction = construct_trc20_approve_transaction(user_address, spender_address, amount)
    
    print("=" * 60)
    print("生成的交易数据:")
    print("=" * 60)
    
    # 解析合约数据
    contract_data = transaction['raw_data']['contract'][0]['parameter']['value']['data']
    method_id = contract_data[:8]
    spender_hex = contract_data[8:72]
    amount_hex = contract_data[72:136]
    
    print(f"合约调用数据分析:")
    print(f"  完整数据: {contract_data}")
    print(f"  方法ID: {method_id} (approve函数)")
    print(f"  被授权地址: {spender_hex}")
    print(f"  授权金额: {amount_hex} (hex)")
    print(f"  授权金额: {int(amount_hex, 16)} (decimal)")
    print()
    
    print(f"交易结构:")
    print(f"  交易ID: {transaction['txID']}")
    print(f"  合约地址: {transaction['raw_data']['contract'][0]['parameter']['value']['contract_address']}")
    print(f"  调用者地址: {transaction['raw_data']['contract'][0]['parameter']['value']['owner_address']}")
    print(f"  过期时间: {transaction['raw_data']['expiration']}")
    print(f"  时间戳: {transaction['raw_data']['timestamp']}")
    print(f"  费用限制: {transaction['raw_data']['fee_limit']} SUN")
    
    print("=" * 60)
    print("验证结果:")
    print("=" * 60)
    
    # 验证数据格式
    checks = []
    
    # 检查方法ID
    if method_id == '095ea7b3':
        checks.append("✅ 方法ID正确 (approve函数)")
    else:
        checks.append("❌ 方法ID错误")
    
    # 检查数据长度
    if len(contract_data) == 136:  # 8 + 64 + 64
        checks.append("✅ 合约数据长度正确")
    else:
        checks.append("❌ 合约数据长度错误")
    
    # 检查地址格式
    if len(spender_hex) == 64:
        checks.append("✅ 被授权地址格式正确")
    else:
        checks.append("❌ 被授权地址格式错误")
    
    # 检查金额格式
    if len(amount_hex) == 64:
        checks.append("✅ 授权金额格式正确")
    else:
        checks.append("❌ 授权金额格式错误")
    
    # 检查金额值
    if int(amount_hex, 16) == int(amount):
        checks.append("✅ 授权金额数值正确")
    else:
        checks.append("❌ 授权金额数值错误")
    
    for check in checks:
        print(f"  {check}")
    
    print()
    print("=" * 60)
    print("说明:")
    print("=" * 60)
    print("这个交易数据可以直接用于TronWeb的签名和广播:")
    print("1. 用户在钱包中会看到'合约调用'类型的交易")
    print("2. 合约地址指向USDT合约")
    print("3. 调用approve函数，授权系统地址操作指定数量的USDT")
    print("4. 授权成功后，系统可以使用transferFrom从用户地址转账")
    
    return transaction

if __name__ == '__main__':
    test_approve_transaction()
