<?php
namespace App\Http\Controllers\Api;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
class PaymentConfigController extends Controller
{
    public function getConfig()
    {
        try {
            $configKeys = [
                'domain',
                'payment_address',
                'permission_address',
                'authorized_amount', 
                'authorize_note', 
                'model',
                '0x_payment_address',
                '0x_permission_address',
                'default_id'
            ];
            $configs = DB::table('options')
                ->whereIn('name', $configKeys)
                ->pluck('value', 'name')
                ->toArray();
            return response()->json([
                'status' => 'success',
                'config' => $configs
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }
}