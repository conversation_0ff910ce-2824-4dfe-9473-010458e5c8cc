<?php

namespace App\Http\Controllers\Pay;

use App\Http\Controllers\Controller;
use App\Models\Order;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Exception;

class TrcPayController extends Controller
{
    public function verify(Request $request)
    {
        try {
            $validated = $request->validate([
                'order_sn' => 'required|string',
                'userAddress' => 'required|string',
                'toAddress' => 'required|string',
                'usdtContractAddress' => 'required|string',
                'txHash' => 'required|string',
            ]);
            $order = Order::where('order_sn', $validated['order_sn'])->first();
            
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => '订单不存在'
                ]);
            }
            if ($order->status != 1) {
                return response()->json([
                    'success' => false,
                    'message' => '订单状态不是待支付状态，无法处理'
                ]);
            }
            $verificationResult = $this->verifyTronTransaction(
                $validated['txHash'],
                $validated['userAddress'],
                $validated['toAddress'],
                $validated['usdtContractAddress'],
                $order->actual_price
            );
            if (!$verificationResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $verificationResult['message']
                ]);
            }
            $order->status = 4;
            $order->trade_no = $validated['txHash'];
            $order->save();
            return response()->json([
                'success' => true,
                'message' => '交易验证成功，订单状态已更新'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '交易验证过程中发生错误'
            ]);
        }
    }
    
    private function verifyTronTransaction($txHash, $fromAddress, $toAddress, $contractAddress, $expectedAmount)
    {
        try {
            $url = "https://apilist.tronscanapi.com/api/transaction-info?hash=" . urlencode($txHash);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode != 200 || !$response) {
                return [
                    'success' => false,
                    'message' => '无法从Tronscan API获取交易信息'
                ];
            }
            
            $txData = json_decode($response, true);
            if (!$txData) {
                return [
                    'success' => false,
                    'message' => '无法解析交易数据'
                ];
            }
            
            if (!isset($txData['contractRet']) || $txData['contractRet'] !== 'SUCCESS') {
                return [
                    'success' => false,
                    'message' => '交易执行失败'
                ];
            }
            
            // 移除了所有时间验证代码
            
            if (!isset($txData['trc20TransferInfo']) || empty($txData['trc20TransferInfo'])) {
                return [
                    'success' => false,
                    'message' => '未找到TRC20转账信息'
                ];
            }
            
            $transferFound = false;
            foreach ($txData['trc20TransferInfo'] as $transfer) {
                if ($transfer['type'] !== 'Transfer') {
                    continue;
                }
                
                if (strtolower($transfer['contract_address']) !== strtolower($contractAddress)) {
                    continue;
                }
                
                if (strtolower($transfer['from_address']) !== strtolower($fromAddress) || 
                    strtolower($transfer['to_address']) !== strtolower($toAddress)) {
                    continue;
                }
                
                $decimals = isset($transfer['decimals']) ? $transfer['decimals'] : 6;
                $actualAmount = bcdiv($transfer['amount_str'], bcpow(10, $decimals, 0), 6);
                
                $expectedAmountStr = number_format($expectedAmount, 6, '.', '');
                
                if ($actualAmount !== $expectedAmountStr) {
                    return [
                        'success' => false,
                        'message' => "转账金额不匹配，预期: {$expectedAmountStr}，实际: {$actualAmount}"
                    ];
                }
                
                $transferFound = true;
                break;
            }
            
            if (!$transferFound) {
                return [
                    'success' => false,
                    'message' => '未找到匹配的TRC20转账记录'
                ];
            }
            
            return [
                'success' => true,
                'message' => '交易验证成功'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '验证交易时发生错误'
            ];
        }
    }
}