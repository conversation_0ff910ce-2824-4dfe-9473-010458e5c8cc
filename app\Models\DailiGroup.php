<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class DailiGroup extends Model
{
    protected $table = 'daili_group';
    
    public $timestamps = false;
    
    protected $fillable = [
        'groupid', 'remark', 'share_profits', 'status'
    ];
    
    protected $casts = [
        'groupid' => 'string',
        'share_profits' => 'float',
        'status' => 'integer'
    ];
    
    public static $validShareProfits = [
        0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0
    ];
    
    public function dailis()
    {
        return $this->hasMany(Daili::class, 'groupid', 'groupid');
    }
}