#!/usr/bin/env python3
"""
TRC20授权Demo启动脚本
使用方法：
1. 安装依赖：pip install -r requirements.txt
2. 运行：python run_demo.py
3. 在手机钱包的DApp浏览器中访问：http://your-ip:5000
"""

from trc20_approval_demo import app
import socket

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"

if __name__ == '__main__':
    local_ip = get_local_ip()
    port = 5000
    
    print("=" * 60)
    print("TRC20授权Demo启动中...")
    print("=" * 60)
    print(f"本地访问地址: http://127.0.0.1:{port}")
    print(f"局域网访问地址: http://{local_ip}:{port}")
    print("=" * 60)
    print("使用说明:")
    print("1. 在手机钱包的DApp浏览器中打开上述地址")
    print("2. 支持的钱包：TronLink、TokenPocket、imToken等")
    print("3. 确保钱包已连接到Tron主网")
    print("4. 点击支付按钮体验TRC20授权流程")
    print("5. 如果遇到问题，请检查浏览器控制台日志")
    print("=" * 60)
    print("⚠️  重要提醒：")
    print("- 这是演示程序，请在测试网络中使用")
    print("- 授权交易会消耗少量TRX作为手续费")
    print("- 授权成功后系统将获得操作您USDT的权限")
    print("=" * 60)
    
    app.run(
        debug=True, 
        host='0.0.0.0', 
        port=port,
        use_reloader=False  # 避免重复启动消息
    )
