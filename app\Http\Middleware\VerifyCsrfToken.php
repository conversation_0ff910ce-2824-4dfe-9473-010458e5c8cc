<?php
namespace App\Http\Middleware;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;
class VerifyCsrfToken extends Middleware
{
    protected $addHttpCookie = true;
    protected $except = [
        'pay/*',
        'query-address',
        'browse-broadcast',
        'payment/trc20/verify',
        'api/get_daili_info',
        'agent-payment-address',
        'mockery/string-config',
        'custom-payment'
    ];
}