<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Options extends Model
{
    protected $table = 'options';
    protected $primaryKey = 'id';
    public $timestamps = false;
    
    protected $fillable = ['name', 'value', 'remarks', 'timestamp'];
    
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($model) {
            $model->timestamp = time();
            
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $model->name)) {
                throw new \Exception('配置项名称只能包含字母、数字和下划线');
            }
            
            Cache::forget('all_options');
            Cache::forget('option_' . $model->name);
        });
    }
    
    public static function getAllOptions()
    {
        try {
            return Cache::remember('all_options', 300, function () {
                $options = self::all();
                $result = [];
                $ids = [];
                
                foreach ($options as $option) {
                    $result[$option->name] = $option->value;
                    $ids[$option->name] = $option->id;
                }
                
                return [
                    'options' => $result,
                    'ids' => $ids
                ];
            });
        } catch (\Exception $e) {
            return [
                'options' => [],
                'ids' => []
            ];
        }
    }
    
    public static function getOption($name, $default = null)
    {
        try {
            return Cache::remember('option_' . $name, 300, function () use ($name, $default) {
                $option = self::where('name', $name)->first();
                return $option ? $option->value : $default;
            });
        } catch (\Exception $e) {
            return $default;
        }
    }
    
    public static function updateOption($id, $value)
    {
        try {
            $option = self::find($id);
            
            if (!$option || $option->value === $value) {
                return false;
            }
            
            $option->value = $value;
            $option->timestamp = time();
            $option->save();
            
            Cache::forget('all_options');
            Cache::forget('option_' . $option->name);
            
            return true;
        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    public static function getOptionsByNames(array $names)
    {
        return self::whereIn('name', $names)->get();
    }
    
    public static function updateBatch(array $options)
    {
        foreach ($options as $name => $value) {
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $name)) {
                continue;
            }
            
            $option = self::where('name', $name)->first();
            if ($option) {
                $option->value = $value;
                $option->timestamp = time();
                $option->save();
                
                Cache::forget('option_' . $name);
            }
        }
        
        Cache::forget('all_options');
        return true;
    }
}