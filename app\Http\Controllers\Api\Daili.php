<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class <PERSON><PERSON> extends Controller
{
    public function getDailiInfo(Request $request)
    {
        $uniqueId = $request->input('unique_id');

        if (!$uniqueId || !preg_match('/^\d{9}$/', $uniqueId)) {
            return response()->json([
                'code' => 'no',
                'msg' => '查询失败'
            ]);
        }
        try {
            $dailiInfo = DB::table('daili')
                ->where('unique_id', $uniqueId)
                ->select('username')
                ->first();

            if ($dailiInfo && !empty($dailiInfo->username) && $dailiInfo->username !== '该用户未设置用户名') {
                return response()->json([
                    'code' => 'yes',
                    'msg' => '获取成功',
                    'data' => [
                        'username' => $dailiInfo->username
                    ]
                ]);
            } else {
                return response()->json([
                    'code' => 'no',
                    'msg' => '查询失败'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'code' => 'no',
                'msg' => '查询失败'
            ]);
        }
    }
}